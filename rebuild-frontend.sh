#!/bin/bash

# Script untuk rebuild frontend dengan environment variables baru

echo "🔄 Rebuilding Frontend dengan Environment Variables Baru..."

# Minta input backend URL
read -p "Masukkan Backend URL dari Claw Cloud (contoh: https://backend-xxxxx.ap-southeast-1.run.claw.cloud): " BACKEND_URL

if [ -z "$BACKEND_URL" ]; then
    echo "❌ Backend URL tidak boleh kosong!"
    exit 1
fi

# Update environment variables
echo "📝 Updating environment variables..."
cat > sumopod/.env << EOF
VITE_API_BASE_URL=$BACKEND_URL
VITE_GOLD_API_URL=https://logam-mulia-api.vercel.app/prices/anekalogam
EOF

echo "🏗️ Building frontend image..."
docker build -t sumopod-project-frontend:latest ./sumopod

echo "✅ Frontend rebuilt dengan environment variables baru!"
echo "Backend URL: $BACKEND_URL"
echo ""
echo "Sekarang jalankan ./docker-push.sh untuk push image yang sudah diupdate"
