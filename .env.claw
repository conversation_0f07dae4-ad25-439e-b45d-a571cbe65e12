# Environment Variables untuk Claw Cloud Deployment

# Database Configuration
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# Authentication Configuration - UPDATE SETELAH DEPLOY BACKEND
BETTER_AUTH_SECRET=9uEY2Wif0Es1Ud4ZGHBmONHjbE4bypDf
BETTER_AUTH_URL=https://your-backend-url.ap-southeast-1.run.claw.cloud
BETTER_AUTH_TRUSTED_ORIGINS=https://your-frontend-url.ap-southeast-1.run.claw.cloud,https://cloone-sumopod.netlify.app

# Xendit Payment Configuration
XENDIT_API_KEY=xnd_development_aRnvtZu0b1pF6huwa1dyMeOgIqk2QELKZr1WTRzKo2zqKs2z1spb4bb3du0PeV
XENDIT_API_URL=https://api.xendit.co/v2/invoices
XENDIT_CALLBACK_TOKEN=sGqcEXjShdnLd4S6DITIbOxbbKWxsPK0018WQb8JqtoHxlo9

# Server Configuration
PORT=8080

# CORS Configuration - UPDATE SETELAH DEPLOY FRONTEND
CORS_ORIGINS=https://your-frontend-url.ap-southeast-1.run.claw.cloud,https://cloone-sumopod.netlify.app
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Session-Token
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS

# Application Configuration
APP_NAME=sumopod-backend
EXTERNAL_ID_PREFIX=sumopod-

# Frontend Configuration
VITE_API_BASE_URL=https://your-backend-url.ap-southeast-1.run.claw.cloud
VITE_GOLD_API_URL=https://logam-mulia-api.vercel.app/prices/anekalogam
