#!/bin/bash

# Script untuk push Docker images ke Docker Hub
# Pastikan sudah login: docker login

echo "🚀 Pushing Sumopod Docker Images to Docker Hub..."

# Minta input username Docker Hub
read -p "Masukkan Docker Hub username Anda: " DOCKER_USERNAME

if [ -z "$DOCKER_USERNAME" ]; then
    echo "❌ Username tidak boleh kosong!"
    exit 1
fi

echo "📦 Tagging backend image..."
docker tag sumopod-project-backend:latest $DOCKER_USERNAME/sumopod-backend:latest

echo "📦 Tagging frontend image..."
docker tag sumopod-project-frontend:latest $DOCKER_USERNAME/sumopod-frontend:latest

echo "⬆️ Pushing backend image..."
docker push $DOCKER_USERNAME/sumopod-backend:latest

echo "⬆️ Pushing frontend image..."
docker push $DOCKER_USERNAME/sumopod-frontend:latest

echo "✅ Done! Images pushed to Docker Hub"
echo ""
echo "📋 Copy URLs ini untuk deploy ke Claw Cloud:"
echo "Backend Image: $DOCKER_USERNAME/sumopod-backend:latest"
echo "Frontend Image: $DOCKER_USERNAME/sumopod-frontend:latest"
echo ""
echo "📖 Baca DEPLOY-CLAW-CLOUD.md untuk panduan lengkap deploy ke Claw Cloud"
