# Deploy Sumopod ke Claw Cloud

## Langkah 1: Push Images ke Docker Hub

1. **Login ke Docker Hub**
```bash
docker login
```

2. **Edit docker-push.sh**
<PERSON>anti `your-dockerhub-username` dengan username Docker Hub Anda:
```bash
nano docker-push.sh
```

3. **Push Images**
```bash
./docker-push.sh
```

## Langkah 2: Deploy Backend ke Claw Cloud

1. **Buka https://ap-southeast-1.run.claw.cloud/**
2. **Klik "Create Application"**
3. **Isi form:**
   - **Image Name**: `your-dockerhub-username/sumopod-backend:latest`
   - **Port**: `80` (masukkan port 80 di form)
   - **Environment Variables** (copy paste satu per satu):
     ```
     DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
     BETTER_AUTH_SECRET=9uEY2Wif0Es1Ud4ZGHBmONHjbE4bypDf
     BETTER_AUTH_URL=https://sumopod-backend-XXXXX.ap-southeast-1.run.claw.cloud
     BETTER_AUTH_TRUSTED_ORIGINS=https://cloone-sumopod.netlify.app
     XENDIT_API_KEY=xnd_development_aRnvtZu0b1pF6huwa1dyMeOgIqk2QELKZr1WTRzKo2zqKs2z1spb4bb3du0PeV
     XENDIT_API_URL=https://api.xendit.co/v2/invoices
     XENDIT_CALLBACK_TOKEN=sGqcEXjShdnLd4S6DITIbOxbbKWxsPK0018WQb8JqtoHxlo9
     PORT=8080
     CORS_ORIGINS=https://cloone-sumopod.netlify.app
     CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Session-Token
     CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
     APP_NAME=sumopod-backend
     EXTERNAL_ID_PREFIX=sumopod-
     ```

4. **Deploy dan catat URL backend yang dihasilkan**

## Langkah 3: Deploy Frontend ke Claw Cloud

1. **Update environment variables frontend**
   Edit file `sumopod/.env` atau rebuild dengan environment baru:
   ```
   VITE_API_BASE_URL=https://your-backend-url.ap-southeast-1.run.claw.cloud
   VITE_GOLD_API_URL=https://logam-mulia-api.vercel.app/prices/anekalogam
   ```

2. **Rebuild frontend image** (jika perlu update env):
```bash
docker build -t sumopod-project-frontend:latest ./sumopod
docker tag sumopod-project-frontend:latest your-dockerhub-username/sumopod-frontend:latest
docker push your-dockerhub-username/sumopod-frontend:latest
```

3. **Deploy frontend di Claw Cloud:**
   - **Image Name**: `your-dockerhub-username/sumopod-frontend:latest`
   - **Port**: `80`
   - **Environment Variables**: (kosong, karena sudah di-build ke dalam image)

## Langkah 4: Update CORS dan Auth URLs

Setelah kedua service deploy:

1. **Update Backend Environment Variables:**
   - `BETTER_AUTH_URL`: URL backend yang baru
   - `CORS_ORIGINS`: tambahkan URL frontend yang baru
   - `BETTER_AUTH_TRUSTED_ORIGINS`: tambahkan URL frontend yang baru

2. **Restart backend service** di Claw Cloud

## Catatan Penting

- **Port**: Gunakan port 80 di form Claw Cloud, tapi aplikasi tetap listen di port yang dikonfigurasi (8080 untuk backend, 80 untuk frontend)
- **Environment Variables**: Masukkan satu per satu di form Claw Cloud
- **URLs**: Akan berbentuk `https://app-name-xxxxx.ap-southeast-1.run.claw.cloud`
- **Database**: Sudah menggunakan Neon PostgreSQL, tidak perlu deploy database terpisah

## Troubleshooting

Jika ada error:
1. Cek logs di Claw Cloud dashboard
2. Pastikan environment variables sudah benar
3. Pastikan image sudah ter-push ke Docker Hub
4. Cek CORS settings jika ada error frontend-backend communication
