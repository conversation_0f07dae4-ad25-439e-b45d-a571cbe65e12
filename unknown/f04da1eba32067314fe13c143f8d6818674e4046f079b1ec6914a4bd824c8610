# fly.toml app configuration file generated for sumopod-backend on 2025-07-10T00:50:44Z
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'sumopod-backend'
primary_region = 'sin'

[build]

[env]
  PORT = '8080'
  # Application Configuration
  APP_NAME = 'sumopod-backend'
  EXTERNAL_ID_PREFIX = 'sumopod-'

  # Authentication Configuration
  BETTER_AUTH_URL = 'https://sumopod-backend.fly.dev'
  BETTER_AUTH_TRUSTED_ORIGINS = 'https://cloone-sumopod.netlify.app'

  # Xendit Configuration
  XENDIT_API_URL = 'https://api.xendit.co/v2/invoices'

  # CORS Configuration
  CORS_ORIGINS = 'http://localhost:3001,https://cloone-sumopod.netlify.app'
  CORS_ALLOW_HEADERS = 'Content-Type,Authorization,X-Session-Token'
  CORS_ALLOW_METHODS = 'GET,POST,PUT,DELETE,OPTIONS'

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
  memory_mb = 1024
