# Docker Setup Sumopod

## Cara Pakai

### 1. Install Docker Desktop
Download dari: https://www.docker.com/products/docker-desktop/

### 2. <PERSON><PERSON> Instalasi
```bash
docker --version
docker-compose --version
```

### 3. Build & Run
```bash
# Build semua image
docker-compose build

# Jalanin semua container
docker-compose up -d

# Cek status
docker-compose ps
```

### 4. Akses Aplikasi
- Frontend: http://localhost:3000
- Backend: http://localhost:8080
- Database: localhost:5432

### 5. Stop Container
```bash
docker-compose down
```

### 6. Build Image Terpisah (kalau mau)
```bash
# Backend
docker build -t sumopod-backend ./hono-backend

# Frontend  
docker build -t sumopod-frontend ./sumopod

# Run manual
docker run -d -p 8080:8080 sumopod-backend
docker run -d -p 3000:80 sumopod-frontend
```

### 7. Liat di Docker Desktop
Buka Docker Desktop, lu bisa liat semua image dan container yang lagi jalan.

## Troubleshooting

### Port udah dipake
```bash
# Cek port yang dipake
lsof -i :3000
lsof -i :8080
lsof -i :5432

# Atau ganti port di docker-compose.yml
```

### Build error
```bash
# Clear cache
docker system prune -a

# Build ulang
docker-compose build --no-cache
```

### Database error
```bash
# Reset database
docker-compose down -v
docker-compose up -d
```

Udah, gitu doang!
